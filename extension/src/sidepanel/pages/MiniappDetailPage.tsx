import { useCallback, useEffect, useRef, useState } from 'react';
import { Tooltip } from 'antd';
import { X } from 'lucide-react';
import historyIcon from '~/assets/icons/history.svg';
import noMiniappImage from '~/assets/imgs/no-miniapp.png';
import { useLiveQuery } from 'dexie-react-hooks';
import { ChatStatus } from '@the-agent/shared';

import { InputArea } from '../components';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { formatDate, getAvatarColor, getInitials } from '~/utils/profile';
import MessageList from '../components/MessageList';
import { ChatHandler } from '~/chat/handler';
import { getMiniapp, updateMiniapp } from '~/services/miniapp';
import { useLanguage } from '~/utils/i18n';
import { refreshThePage } from '~/utils/toolkit';

export const MiniappDetailPage = () => {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { getMessage } = useLanguage();
  const miniapp = useLiveQuery(() => getMiniapp(Number(id)), [id]);
  const [prompt, setPrompt] = useState('');
  const [status, setStatus] = useState<ChatStatus>('idle');
  const [chatHandler, setChatHandler] = useState<ChatHandler | null>(null);

  // Get plugin type from URL parameters
  const pluginType = searchParams.get('pluginType') as 'script' | 'workflow';

  const [showHistoricalVersions, setShowHistoricalVersions] = useState(false);

  // measure the height of the info + optimized versions list
  const topBlockRef = useRef<HTMLDivElement | null>(null);
  const [topOffset, setTopOffset] = useState(0);
  const HEADER_HEIGHT = 44;

  const handleBack = () => {
    if (showHistoricalVersions) {
      setShowHistoricalVersions(false);
    } else {
      navigate('/miniapps');
    }
  };

  useEffect(() => {
    if (miniapp?.conversation_id !== -1) {
      setChatHandler(
        new ChatHandler({
          currentConversationId: miniapp?.conversation_id ?? 0,
          conversationType: 'miniapp',
          setStatus,
        })
      );
    }
  }, [miniapp?.conversation_id, setStatus]);

  const abort = useCallback(() => {
    chatHandler?.abort();
  }, [chatHandler]);

  const handleUninstall = useCallback(async () => {
    if (!miniapp?.id) return;
    try {
      await updateMiniapp(miniapp.id, { installation: null });
      await refreshThePage();
    } catch (error) {
      console.error('Failed to uninstall miniapp:', error);
    }
  }, [miniapp?.id]);

  const welcomeComponent = () => {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          padding: '20px 20px 40px 20px',
          gap: '24px',
        }}
      >
        {/* Illustration */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '8px',
          }}
        >
          <img
            src={noMiniappImage}
            alt="No MiniApp illustration"
            style={{
              width: '200px',
              height: 'auto',
              opacity: 0.8,
            }}
          />
        </div>

        {/* Text Content */}
        <div
          style={{
            textAlign: 'center',
            maxWidth: '300px',
          }}
        >
          <h2
            style={{
              fontSize: '20px',
              fontWeight: 600,
              color: '#111827',
              margin: '0 0 8px 0',
              lineHeight: 1.3,
            }}
          >
            {getMessage('emptyMiniapps')}
          </h2>
        </div>
      </div>
    );
  };

  // measure the combined height of the info section and the optimized versions list
  useEffect(() => {
    const element = topBlockRef.current;
    if (!element) return;

    const updateHeight = () => {
      const measured = element.getBoundingClientRect().height;
      setTopOffset(HEADER_HEIGHT + measured);
    };

    updateHeight();

    const resizeObserver = new ResizeObserver(() => updateHeight());
    resizeObserver.observe(element);
    return () => resizeObserver.disconnect();
  }, [miniapp?.id, miniapp?.history?.length]);

  // Handle case where no specific miniapp is selected (coming from tab navigation)
  const isNewMode = !id || !miniapp || miniapp.id === -1;

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100vh', width: '100%' }}>
      {/* Header */}
      <div style={{ position: 'sticky', top: 0, zIndex: 10, backgroundColor: '#ffffff' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor: '#ffffff',
            padding: '0 16px',
            height: '44px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>
            {showHistoricalVersions
              ? getMessage('miniappDetailHeaderHistory')
              : isNewMode
                ? getMessage('miniappDetailHeaderDevelop', pluginType || 'script')
                : getMessage('miniappDetailHeaderDevelop', pluginType)}
          </h2>
          <Tooltip title={getMessage('tooltipClose')} placement="bottom">
            <button
              onClick={handleBack}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>
      </div>

      {/* Content */}
      <div style={{ display: 'flex', flexDirection: 'column', flex: 1, overflow: 'hidden' }}>
        {/* Display miniapp info at top + Optimized versions list (measured area) - only for existing miniapps */}
        {!isNewMode && (
          <div ref={topBlockRef}>
            {/* Display miniapp info at top */}
            <div style={{ padding: '16px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <div
                  style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: '12px',
                    backgroundColor: getAvatarColor(miniapp!.name),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#ffffff',
                    fontSize: '14px',
                    fontWeight: 600,
                    flexShrink: 0,
                  }}
                >
                  {getInitials(miniapp!.name)}
                </div>
                <div style={{ flex: 1, minWidth: 0 }}>
                  <div
                    style={{
                      fontSize: '16px',
                      fontWeight: 600,
                      color: '#111827',
                      marginBottom: '2px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {miniapp!.name}
                  </div>
                  <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '2px' }}>
                    {getMessage('miniappDetailVersion', String(miniapp?.history?.length ?? '1'))}
                  </div>
                  <div style={{ fontSize: '12px', color: '#6b7280' }}>
                    {getMessage(
                      'miniappDetailInstalledOn',
                      formatDate(miniapp?.installation?.deployed_at || 0)
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Historical versions list - show if miniapp has history and showHistoricalVersions is true */}
            {showHistoricalVersions && miniapp?.history && miniapp?.history.length > 0 && (
              <div
                style={{
                  padding: '0px 16px 16px 16px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '12px',
                }}
              >
                {miniapp?.history.map((version, index) => (
                  <div
                    key={index}
                    style={{
                      padding: '12px',
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      backgroundColor: '#ffffff',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                    }}
                    onMouseOver={e => {
                      e.currentTarget.style.backgroundColor = '#f9fafb';
                      e.currentTarget.style.borderColor = '#d1d5db';
                    }}
                    onMouseOut={e => {
                      e.currentTarget.style.backgroundColor = '#ffffff';
                      e.currentTarget.style.borderColor = '#e5e7eb';
                    }}
                  >
                    <div
                      style={{
                        width: '32px',
                        height: '32px',
                        borderRadius: '6px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <img
                        src={historyIcon}
                        alt="History"
                        style={{ width: '16px', height: '16px' }}
                      />
                    </div>
                    <div style={{ flex: 1 }}>
                      <div
                        style={{
                          fontSize: '14px',
                          fontWeight: 600,
                          color: '#111827',
                          marginBottom: '2px',
                        }}
                      >
                        {getMessage(
                          'miniappDetailVersion',
                          String((miniapp?.history.length ?? 0) - index)
                        )}
                      </div>
                      <div style={{ fontSize: '12px', color: '#6b7280' }}>
                        {getMessage(
                          'miniappDetailGeneratedOn',
                          formatDate(version.deployed_at ?? 0)
                        )}
                      </div>
                    </div>
                    <div style={{ color: '#6b7280' }}>
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <polyline points="9,18 15,12 9,6"></polyline>
                      </svg>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* View historical versions section - Show if miniapp is deployed and showHistoricalVersions is false */}
            {!showHistoricalVersions && miniapp?.installation && (
              <>
                <div
                  style={{
                    padding: '0px 16px 16px 16px',
                    backgroundColor: '#ffffff',
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '8px',
                  }}
                >
                  <button
                    onClick={() => setShowHistoricalVersions(true)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '8px 12px',
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      backgroundColor: '#ffffff',
                      color: '#374151',
                      fontSize: '14px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      flex: 1,
                      justifyContent: 'space-between',
                    }}
                    onMouseOver={e => {
                      e.currentTarget.style.backgroundColor = '#f9fafb';
                      e.currentTarget.style.borderColor = '#d1d5db';
                    }}
                    onMouseOut={e => {
                      e.currentTarget.style.backgroundColor = '#ffffff';
                      e.currentTarget.style.borderColor = '#e5e7eb';
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <img
                        src={historyIcon}
                        alt="History"
                        style={{ width: '16px', height: '16px' }}
                      />
                      <span>{getMessage('miniappDetailViewAllVersions')}</span>
                    </div>
                  </button>

                  <button
                    onClick={handleUninstall}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '8px 12px',
                      borderRadius: '8px',
                      border: '1px solid #dc2626',
                      backgroundColor: '#ffffff',
                      color: '#dc2626',
                      fontSize: '14px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      minWidth: '120px',
                      justifyContent: 'center',
                    }}
                    onMouseOver={e => {
                      e.currentTarget.style.backgroundColor = '#fef2f2';
                      e.currentTarget.style.borderColor = '#b91c1c';
                    }}
                    onMouseOut={e => {
                      e.currentTarget.style.backgroundColor = '#ffffff';
                      e.currentTarget.style.borderColor = '#dc2626';
                    }}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M3 6h18v2H3V6zm2 3h14v11a2 2 0 01-2 2H7a2 2 0 01-2-2V9zm3-7h6v2H8V2zm2 7v8h2V9h-2zm4 0v8h2V9h-2z" />
                    </svg>
                    <span>{getMessage('uninstall')}</span>
                  </button>
                </div>
              </>
            )}
          </div>
        )}

        {/* Messages Area (scrolls) */}
        {!showHistoricalVersions && (
          <MessageList
            convId={miniapp?.conversation_id ?? 0}
            workflowMode={false}
            welcomeComponent={welcomeComponent()}
            status={status}
            hasBanner={false}
            top={isNewMode ? HEADER_HEIGHT : topOffset}
            miniapp={miniapp}
          />
        )}
      </div>

      {/* Input Area (bottom) - only show for existing miniapps */}
      {!isNewMode && (
        <InputArea
          prompt={prompt}
          setPrompt={setPrompt}
          onSubmitRich={chatHandler?.handleSubmit}
          status={status}
          abort={abort}
          miniapp={miniapp}
        />
      )}
    </div>
  );
};
