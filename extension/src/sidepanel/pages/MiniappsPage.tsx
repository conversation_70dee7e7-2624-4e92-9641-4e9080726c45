import { useMemo, useState, useEffect } from 'react';
import { MiniApp } from '@the-agent/shared';
import { Modal, Input } from 'antd';

import { useLiveQuery } from 'dexie-react-hooks';
import { db } from '~/storages/indexdb';
import MiniappEmptyState from '~/sidepanel/components/miniapp/MiniappEmptyState';
import MiniappApplicationsList from '~/sidepanel/components/miniapp/MiniappApplicationsList';
import newMiniappImg from '~/assets/imgs/new-miniapp.png';
import { useNavigate, useLocation } from 'react-router-dom';
import { useUser } from '~/hooks/useUser';
import { createNewMiniApp } from '~/services/conversation';
import {
  deleteMiniapp as deleteMiniappService,
  updateMiniapp as updateMiniappService,
} from '~/services/miniapp';
import { useLanguage } from '~/utils/i18n';
import Header from '~/sidepanel/components/Header';

type FilterType = 'All' | 'Installed' | 'Uninstalled';
type TabType = 'script' | 'workflow';

export const MiniappsPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { getMessage } = useLanguage();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [projectName, setProjectName] = useState('');
  const [showArchivedView, setShowArchivedView] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState<number | null>(null);
  const [showMiniappApplicationsList, setShowMiniappApplicationsList] = useState(false);
  const activeUser = useUser();
  const [filter, setFilter] = useState<FilterType>('All');
  const [activeTab, setActiveTab] = useState<TabType>('script');

  // Get type from URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const type = urlParams.get('type') as TabType;
    if (type === 'workflow' || type === 'script') {
      setActiveTab(type);
    }
  }, [location.search]);

  const miniapps = useLiveQuery(async () => {
    if (!activeUser?.id) return [];
    if (showArchivedView) {
      const archivedApps = await db.getArchivedApps();
      return archivedApps;
    }
    const allApps = await db.getAllApps('All');
    return allApps;
  }, [activeUser?.id, showArchivedView]);

  // Filter miniapps by tab and filter
  const filteredMiniapps = useMemo(() => {
    if (!miniapps) return [];

    // First filter by active tab (type)
    const tabFilteredApps = miniapps.filter(app => app.type === activeTab);

    // Then apply the installation filter
    if (filter === 'All') return tabFilteredApps;
    if (filter === 'Installed')
      return tabFilteredApps.filter(app => app.installation && app.installation.code);
    if (filter === 'Uninstalled')
      return tabFilteredApps.filter(app => !app.installation || !app.installation.code);
    return tabFilteredApps;
  }, [miniapps, filter, activeTab]);

  const handleCreateProject = async () => {
    if (!projectName.trim() || !activeUser?.id) return;

    try {
      const miniapp = await createNewMiniApp(activeUser.id, projectName.trim(), activeTab);
      navigate(`/miniapp/${miniapp.id}?pluginType=${activeTab}`);

      setShowCreateModal(false);
      setProjectName('');
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const handleCancelCreate = () => {
    setShowCreateModal(false);
    setProjectName('');
  };

  const handleSelectMiniapp = (miniapp: MiniApp) => {
    navigate(`/miniapp/${miniapp.id}?pluginType=${miniapp.type}`);
  };

  const handleArchiveMiniapp = async (miniapp: MiniApp) => {
    try {
      await updateMiniappService(miniapp.id, { status: 'archived' });
    } catch (error) {
      console.error('Failed to archive miniapp:', error);
    }
  };

  const handleActivateMiniapp = async (miniapp: MiniApp) => {
    try {
      await updateMiniappService(miniapp.id, { status: 'active' });
    } catch (error) {
      console.error('Failed to activate miniapp:', error);
    }
  };

  const handleDeleteMiniapp = (miniapp: MiniApp) => {
    setConfirmDelete(miniapp.id);
  };

  const handleCancelDelete = () => setConfirmDelete(null);

  const handleConfirmDelete = async () => {
    if (confirmDelete == null) return;
    try {
      await deleteMiniappService(confirmDelete);
    } catch (error) {
      console.error('Failed to delete miniapp:', error);
    } finally {
      setConfirmDelete(null);
    }
  };

  const handleShowArchived = () => {
    setShowArchivedView(true);
  };

  const toggleMiniappApplicationsList = (value?: boolean) => {
    const willShow = value !== undefined ? value : !showMiniappApplicationsList;
    setShowMiniappApplicationsList(willShow);
  };

  return (
    <div
      style={{
        height: '100vh',
        width: '100%',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Header */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          backgroundColor: 'white',
          zIndex: 10,
        }}
      >
        <Header
          setShowConversationList={() => {}} // Not used in MiniappsPage
          setShowMiniappApplicationsList={toggleMiniappApplicationsList}
          user={activeUser}
        />
      </div>

      {/* Main Content */}
      <div
        style={{
          position: 'absolute',
          top: '44px',
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: '#ffffff',
          overflow: 'hidden',
        }}
      >
        {/* Content */}
        <div
          style={{
            flex: 1,
            height: 'calc(100vh - 44px)',
          }}
        >
          {/* TODO: Main content area show MiniappDetailPage */}
          <MiniappEmptyState fromArchived={showArchivedView} />
        </div>
      </div>

      {/* Miniapp Applications List */}
      {showMiniappApplicationsList && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 50,
            animation: 'fadeIn 0.2s ease-out',
          }}
          onClick={() => setShowMiniappApplicationsList(false)}
        >
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '320px',
              height: '100%',
              boxShadow: '2px 0 10px rgba(0, 0, 0, 0.1)',
              animation: 'slideInFromLeft 0.3s ease-out',
              overflow: 'hidden',
            }}
            onClick={e => e.stopPropagation()}
          >
            <MiniappApplicationsList
              miniapps={filteredMiniapps || []}
              onSelectMiniapp={handleSelectMiniapp}
              onArchiveMiniapp={handleArchiveMiniapp}
              onDeleteMiniapp={handleDeleteMiniapp}
              onActivateMiniapp={handleActivateMiniapp}
              onShowArchived={handleShowArchived}
              isArchivedView={showArchivedView}
              onClose={() => setShowMiniappApplicationsList(false)}
              filter={filter}
              onFilterChange={setFilter}
              activeTab={activeTab}
            />
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        open={confirmDelete != null}
        onCancel={handleCancelDelete}
        footer={
          <div style={{ display: 'flex', justifyContent: 'center', gap: 16, marginTop: 8 }}>
            <button
              onClick={handleCancelDelete}
              style={{
                fontWeight: 500,
                fontSize: 15,
                padding: '9px 22px',
                borderRadius: 7,
                border: '1px solid #D1D5DB',
                color: '#111827',
                background: '#fff',
                cursor: 'pointer',
                transition: 'background 0.2s, border 0.2s',
              }}
            >
              {getMessage('cancel')}
            </button>
            <button
              onClick={handleConfirmDelete}
              style={{
                fontWeight: 600,
                fontSize: 15,
                padding: '9px 22px',
                borderRadius: 7,
                border: 'none',
                background: '#DC2626',
                color: '#fff',
                cursor: 'pointer',
                boxShadow: '0 2px 8px 0 rgba(220,38,38,0.08)',
                transition: 'background 0.2s',
              }}
            >
              {getMessage('delete')}
            </button>
          </div>
        }
        centered
        closable={false}
        width={300}
        styles={{
          mask: { background: 'rgba(0,0,0,0.18)' },
          content: { borderRadius: 24 },
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <h3 style={{ fontSize: 22, fontWeight: 700, marginBottom: 18 }}>
            {getMessage('deleteMiniappTitle')}
          </h3>
          <div style={{ fontSize: 17, color: '#374151', marginBottom: 28 }}>
            {getMessage('deleteMiniappContent')}
          </div>
        </div>
      </Modal>

      {/* Create Project Modal */}
      <Modal
        open={showCreateModal}
        onCancel={handleCancelCreate}
        footer={null}
        centered
        width={300}
        closable={false}
      >
        <div style={{ textAlign: 'center' }}>
          <img
            src={newMiniappImg}
            alt="Create Project"
            style={{
              width: '80px',
              height: '80px',
              objectFit: 'contain',
            }}
          />

          {/* Title */}
          <h2
            style={{
              fontSize: '16px',
              fontWeight: 600,
              color: '#111827',
              marginBottom: '24px',
              margin: '0 0 24px 0',
            }}
          >
            {getMessage('createProjectTitle')}
          </h2>

          {/* Form */}
          <div style={{ textAlign: 'left', marginBottom: '24px' }}>
            <label
              style={{
                display: 'block',
                fontSize: '14px',
                fontWeight: 500,
                color: '#374151',
                marginBottom: '6px',
              }}
            >
              {getMessage('projectNameLabel')}
            </label>
            <Input
              placeholder={getMessage('pleaseEnterPlaceholder')}
              value={projectName}
              onChange={e => setProjectName(e.target.value)}
              onPressEnter={handleCreateProject}
              style={{
                height: '40px',
                fontSize: '14px',
                borderRadius: '6px',
              }}
              autoFocus
            />
          </div>

          {/* Buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            <button
              onClick={handleCreateProject}
              disabled={!projectName.trim()}
              style={{
                width: '100%',
                height: '40px',
                borderRadius: '6px',
                border: 'none',
                backgroundColor: projectName.trim() ? '#000' : '#d1d5db',
                color: '#ffffff',
                fontSize: '14px',
                fontWeight: 500,
                cursor: projectName.trim() ? 'pointer' : 'not-allowed',
                transition: 'background-color 0.2s',
              }}
            >
              {getMessage('create')}
            </button>
            <button
              onClick={handleCancelCreate}
              style={{
                width: '100%',
                height: '40px',
                borderRadius: '6px',
                border: '1px solid #d1d5db',
                backgroundColor: '#ffffff',
                color: '#374151',
                fontSize: '14px',
                fontWeight: 500,
                cursor: 'pointer',
                transition: 'all 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#ffffff';
              }}
            >
              {getMessage('cancel')}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
